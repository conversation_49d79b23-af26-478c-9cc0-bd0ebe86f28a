package main

import (
	"fmt"
	"io/ioutil"
	"net/http"
	"os"
	"sync"

	"github.com/robfig/cron"
)

type APIResponse struct {
	URL      string
	Response string
}

func main() {
	c := cron.New()
	API_BASE := os.Getenv("API_PUB_BASE")

	cronJobs := []struct {
		schedule string
		paths    []string
	}{
		// Run every 1 second
		{"* * * * * *", []string{
			"/api/cron/orders",
			"/api/cron/site/finances",
		}},

		// Run every 10 seconds
		{"*/10 * * * * *", []string{
			"/api/cron/nutifood/get_hub_stocks",
		}},

		// Run every 10 seconds
		{"*/10 * * * * *", []string{
			"/api/cron/token_account/refresh",
			"/api/cron/nutifood/sync_stocks",
			"/api/cron/auto-confirms",
			"/api/cron/auto-print-bills",
			// "/api/cron/auto-generate-bills",
			// "/api/cron/auto-print-labels",
		}},

		// Run every 30 seconds
		{"*/30 * * * * *", []string{
			"/api/cron/notifications",
			"/api/cron/nutifood/sync_orders",
			"/api/cron/nexpos/sync_orders",
			"/v1/order-service/cron/notifications",
		}},

		// Run every minute
		{"0 * * * * *", []string{
			"/api/cron/orders_in_x_days",
			"/api/cron/notifications",
			"/api/cron/order/notification/remind",
			"/api/cron/order/notification/cancel",
			"/api/cron/ecom/orders",
			"/api/cron/site/feedbacks",
			"/api/cron/site/feedback_summary",
			"/api/cron/site/incidents",
		}},

		// Run every minute between 2:00 AM to 3:00 PM
		{"0 * 2-15 * * *", []string{
			"/api/cron/healths",
		}},

		// Run every 5 minutes
		{"0 */5 * * * *", []string{
			"/api/cron/order/total_checking",
		}},

		// Run at midnight on the 1st day of each month
		{"0 0 1 * *", []string{
			"/api/cron/partner-commission",
		}},

		// Run at the beginning of every hour
		{"0 0 * * *", []string{
			"/api/cron/customer_profiles/sync",
			"/api/cron/update_it_tickets",
		}},
		// Run at the beginning of every hour
		{"0 0 * * *", []string{
			"/api/cron/update_it_tickets",
		}},
		// Run at every 10 seconds
		{"*/10 * * * * *", []string{
			"/api/cron/core-product/sync-stocks",
		}},
	}

	externalCronJobs := []struct {
		schedule string
		paths    []string
	}{
		// Run every 10 seconds
		{"*/10 * * * * *", []string{
			"https://pay.nexdor.tech/api/cron/transactions",
		}},
	}

	saasCronJobs := []struct {
		schedule string
		paths    []string
	}{
		// Run every 5 minutes
		{"0 */5 * * * *", []string{
			"https://saas-api-dev.nexpos.io/v1/merchant-service/cron/orders",
			"https://saas-api-dev.nexpos.io/v1/merchant-service/cron/orders",
			"https://saas-api.nexpos.io/v1/merchant-service/cron/orders",
		}},

		// Run every 10 seconds
		{"*/10 * * * * *", []string{
			"https://saas-api-dev.nexpos.io/v1/order-service/cron/notifications",
		}},
	}

	for _, job := range cronJobs {
		urls := make([]string, len(job.paths))
		for i, path := range job.paths {
			urls[i] = fmt.Sprintf("%s%s", API_BASE, path)
		}
		if err := c.AddFunc(job.schedule, func() { fetchAPIs(urls) }); err != nil {
			fmt.Println("Error adding cron job:", err)
			return
		}
	}

	for _, job := range externalCronJobs {
		if err := c.AddFunc(job.schedule, func() { fetchAPIs(job.paths) }); err != nil {
			fmt.Println("Error adding cron job:", err)
			return
		}
	}

	for _, job := range saasCronJobs {
		if err := c.AddFunc(job.schedule, func() { fetchAPIs(job.paths) }); err != nil {
			fmt.Println("Error adding cron job:", err)
			return
		}
	}

	c.Start()
	select {}
}

func fetchAPIs(urls []string) {
	var wg sync.WaitGroup
	responseCh := make(chan APIResponse, len(urls))

	for _, url := range urls {
		wg.Add(1)
		go func(url string) {
			defer wg.Done()
			response, err := callAPI(url)
			if err != nil {
				fmt.Printf("Error calling %s: %v\n", url, err)
				return
			}
			responseCh <- APIResponse{URL: url, Response: response}
		}(url)
	}

	wg.Wait()
	close(responseCh)

	for response := range responseCh {
		fmt.Printf("Response from %s: %s\n", response.URL, response.Response)
	}
}

func callAPI(url string) (string, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", err
	}

	INTERNAL_API_KEY := os.Getenv("INTERNAL_API_KEY")
	req.Header.Set("x-access-token", INTERNAL_API_KEY)

	client := http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	return string(body), nil
}
