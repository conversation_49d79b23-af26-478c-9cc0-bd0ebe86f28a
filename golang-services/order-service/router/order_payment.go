package router

import (
	"errors"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/gin-gonic/gin"
	"github.com/samber/lo"
	"gorm.io/gorm"

	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/middlewares"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/models"
	"github.com/nexdorvn/nexpos-backend/golang-services/sdk/payment"
)

// PaymentService represents a payment method configuration
type PaymentService struct {
	Vendor      string `json:"vendor"`
	Name        string `json:"name"`
	Description string `json:"description"`
	IsActive    bool   `json:"is_active"`
}

// DefaultPaymentServices contains the list of default available payment methods
var DefaultPaymentServices = []PaymentService{
	{
		Vendor:      "CASH",
		Name:        "Tiền mặt",
		Description: "<PERSON>h toán bằng tiền mặt khi nhận hàng",
		IsActive:    true,
	},
	{
		Vendor:      "COD",
		Name:        "<PERSON><PERSON> toán khi nhận hàng",
		Description: "Vui lòng thanh toán cho tài xế sau khi bạn kiểm tra đơn hàng",
		IsActive:    true,
	},
	{
		Vendor:      "NEXDORPAY",
		Name:        "NexDorPay",
		Description: "NexDorPay",
		IsActive:    true,
	},
	{
		Vendor:      "VNPAY",
		Name:        "VNPay",
		Description: "Thanh toán qua VNPay",
		IsActive:    true,
	},
	{
		Vendor:      "VNPAY_QR",
		Name:        "VNPay QR",
		Description: "Thanh toán VNPay bằng mã QR",
		IsActive:    true,
	},
	{
		Vendor:      "ESTEEM_GIFT",
		Name:        "Esteem Gift",
		Description: "Thanh toán bằng Esteem Gift",
		IsActive:    true,
	},
	{
		Vendor:      "GOTIT",
		Name:        "Gotit",
		Description: "Thanh toán bằng Gotit",
		IsActive:    true,
	},
	{
		Vendor:      "GIFTPOP",
		Name:        "Giftpop",
		Description: "Thanh toán bằng Giftpop",
		IsActive:    true,
	},
	{
		Vendor:      "VOUCHER",
		Name:        "Voucher",
		Description: "Thanh toán bằng Voucher",
		IsActive:    true,
	},
	{
		Vendor:      "BANK_TRANSFER",
		Name:        "Chuyển khoản thủ công",
		Description: "Thanh toán bằng chuyển khoản thủ công",
		IsActive:    true,
	},
}

func GetOrderPaymentURLs(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")

	// Find the order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Handle CASH payment method
	paymentMethod := c.Query("payment_method")
	if paymentMethod == "CASH" {
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
		return
	}

	// Find site and brand
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var brand models.Brand
	if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Handle different payment methods
	switch {
	case strings.HasPrefix(paymentMethod, "MOMO"):
		handleMomoPayment(c, db, order, site, brand, paymentMethod)
	case paymentMethod == "NEXDORPAY":
		handleNexdorPayment(c, db, order, site, brand)
	case strings.HasPrefix(paymentMethod, "VNPAY"):
		handleVNPayPayment(c, db, order, site, brand, paymentMethod)
	default:
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
	}
}

func RePaymentForOrder(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")
	paymentMethod := c.Query("payment_method")

	// Validate payment method
	if paymentMethod == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "payment_method_is_empty",
		})
		return
	}

	// Find the order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Check order status
	if !lo.Contains([]string{"PENDING", "WAITING_PAYMENT"}, order.Status) {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "order_in_process_can_not_payment",
		})
		return
	}

	// Find site and brand
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	var brand models.Brand
	if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Handle different payment methods
	switch {
	case strings.HasPrefix(paymentMethod, "MOMO"):
		handleMomoPayment(c, db, order, site, brand, paymentMethod)
	case paymentMethod == "NEXDORPAY":
		handleNexdorPayment(c, db, order, site, brand)
	case strings.HasPrefix(paymentMethod, "VNPAY"):
		handleVNPayPayment(c, db, order, site, brand, paymentMethod)
	default:
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
	}
}

func GetOrderTransaction(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")
	transactionID := c.Param("transaction_id")

	// Find the order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "order_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find the transaction
	var transaction models.OrderPayment
	if err := db.Where("order_id = ? AND transaction_id = ?", orderID, transactionID).
		Select("id, transaction_id, status").
		First(&transaction).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			c.JSON(http.StatusNotFound, gin.H{
				"success": false,
				"error":   "transaction_not_found",
			})
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    transaction,
	})
}

func GetPublicOrderPaymentURLs(c *gin.Context) {
	db := middlewares.GetDB(c)
	orderID := c.Param("order_id")
	paymentMethod := c.Query("payment_method")

	// Find the order
	var order models.Order
	if err := db.Where("order_id = ?", orderID).First(&order).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			storeWebURL := os.Getenv("STORE_WEB_URL")
			redirectURL := fmt.Sprintf("%s/%s/payment?success=false&order_id=%s&payment_method=%s",
				storeWebURL, "site-code", orderID, paymentMethod)
			c.Redirect(http.StatusFound, redirectURL)
			return
		}
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Find site
	var site models.Site
	if err := db.Where("id = ?", order.SiteID).First(&site).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "site_not_found",
		})
		return
	}

	// Handle CASH payment method
	if paymentMethod == "CASH" {
		storeWebURL := os.Getenv("STORE_WEB_URL")
		redirectURL := fmt.Sprintf("%s/%s/payment?success=true&order_id=%s&payment_method=%s",
			storeWebURL, site.Code, orderID, paymentMethod)
		c.Redirect(http.StatusFound, redirectURL)
		return
	}

	// Find brand
	var brand models.Brand
	if err := db.Where("id = ?", site.BrandID).First(&brand).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "brand_not_found",
		})
		return
	}

	// Handle different payment methods
	switch {
	case strings.HasPrefix(paymentMethod, "MOMO"):
		handleMomoPayment(c, db, order, site, brand, paymentMethod)
	case paymentMethod == "NEXDORPAY":
		handleNexdorPayment(c, db, order, site, brand)
	case strings.HasPrefix(paymentMethod, "VNPAY"):
		handleVNPayPayment(c, db, order, site, brand, paymentMethod)
	default:
		c.JSON(http.StatusOK, gin.H{
			"success": true,
		})
	}
}

func ShareOrderPaymentURL(c *gin.Context) {
	orderID := c.Query("order_id")
	paymentMethod := c.Query("payment_method")

	apiBase := os.Getenv("API_BASE")
	redirectURL := fmt.Sprintf("%s/v1/order-service/public/site/orders/%s/payment_urls?payment_method=%s",
		apiBase, orderID, paymentMethod)

	c.Redirect(http.StatusFound, redirectURL)
}

// GetPaymentServices returns the list of available payment services
func GetPaymentServices(c *gin.Context) {
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    DefaultPaymentServices,
	})
}

func handleMomoPayment(c *gin.Context, db *gorm.DB, order models.Order, site models.Site, brand models.Brand, paymentMethod string) {
	// In a real implementation, this would call the Momo payment service
	// For now, we'll just return a placeholder response

	// Find payment amount from order
	var paymentAmount float64
	for _, payment := range order.DataMapping.Data.Payments {
		if payment.Method == paymentMethod {
			paymentAmount = payment.Total
			break
		}
	}

	if paymentAmount == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "total_not_found",
		})
		return
	}

	// Create a mock payment URL response
	paymentURL := map[string]any{
		"pay_url":        fmt.Sprintf("https://payment.momo.vn/%s", order.OrderID),
		"deeplink":       fmt.Sprintf("momo://payment?order=%s", order.OrderID),
		"qrCodeUrl":      fmt.Sprintf("https://storage.googleapis.com/nexpos-files/qr/momo_%s.png", order.OrderID),
		"transaction_id": fmt.Sprintf("MOMO_%s", order.OrderID),
	}

	// Create order payment record
	orderPayment := models.OrderPayment{
		Vendor:        "momo",
		OrderID:       order.OrderID,
		TransactionID: paymentURL["transaction_id"].(string),
		Amount:        paymentAmount,
		Status:        "PENDING",
		PaymentData:   []byte(`{"pay_url":"` + paymentURL["pay_url"].(string) + `"}`),
	}

	if err := db.Create(&orderPayment).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    paymentURL,
	})
}

func handleNexdorPayment(c *gin.Context, db *gorm.DB, order models.Order, site models.Site, brand models.Brand) {
	// In a real implementation, this would call the NexDorPay service
	// For now, we'll just return a placeholder response

	// Find payment amount from order
	var paymentAmount float64
	for _, payment := range order.DataMapping.Data.Payments {
		if payment.Method == "NEXDORPAY" {
			paymentAmount = payment.Total
			break
		}
	}

	if paymentAmount == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "total_not_found",
		})
		return
	}

	// Create a mock payment URL response
	paymentURL := map[string]any{
		"pay_url":        fmt.Sprintf("https://pay.nexdor.tech?tx=NDP_%s", order.OrderID),
		"qr_code":        fmt.Sprintf("https://storage.googleapis.com/nexpos-files/qr/nexdorpay_%s.png", order.OrderID),
		"transaction_id": fmt.Sprintf("NDP_%s", order.OrderID),
	}

	// Create order payment record
	orderPayment := models.OrderPayment{
		Vendor:        "nexdorpay",
		OrderID:       order.OrderID,
		TransactionID: paymentURL["transaction_id"].(string),
		Amount:        paymentAmount,
		Status:        "PENDING",
		Description:   "Đang chờ thanh toán",
		PaymentData:   []byte(`{"pay_url":"` + paymentURL["pay_url"].(string) + `"}`),
	}

	if err := db.Create(&orderPayment).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    paymentURL,
	})
}

func handleVNPayPayment(c *gin.Context, db *gorm.DB, order models.Order, site models.Site, brand models.Brand, paymentMethod string) {
	// Find payment amount from order
	var paymentAmount float64
	for _, payment := range order.DataMapping.Data.Payments {
		if strings.HasPrefix(payment.Method, "VNPAY") {
			paymentAmount = payment.Total
			break
		}
	}

	if paymentAmount == 0 {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "total_not_found",
		})
		return
	}

	// Get VNPay token from site or brand
	var vnpayToken *models.Token

	// Try to get VNPay token from site
	siteToken := site.GetToken("vnpay")
	if siteToken != nil {
		vnpayToken = &models.Token{
			SiteID:   siteToken.SiteID,
			Username: "", // VNPay doesn't use username
			Password: "", // Will be set from token data
		}
		// Extract password from site data if available
		// This would need to be implemented based on how VNPay tokens are stored
	}

	// Create VNPay payment provider
	provider := payment.NewProvider("vnpay")
	if provider == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "vnpay_provider_not_available",
		})
		return
	}

	// Prepare payment request
	paymentRequest := &payment.PaymentRequest{
		OrderID:        order.OrderID,
		Amount:         paymentAmount,
		Description:    fmt.Sprintf("Thanh toán đơn hàng %s", order.OrderID),
		ClientCallback: fmt.Sprintf("%s/v1/order-service/public/site/orders/%s/payment_urls?payment_method=%s&success=true", os.Getenv("API_BASE"), order.OrderID, paymentMethod),
		ServerCallback: fmt.Sprintf("%s/v1/order-service/vnpay/callbacks", os.Getenv("API_BASE")),
		PaymentMethod:  paymentMethod,
		Language:       "vi",
	}

	// Create payment link
	response, err := provider.CreatePaymentLink(vnpayToken, paymentRequest)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("vnpay_payment_creation_failed: %v", err),
		})
		return
	}

	// Create order payment record
	orderPayment := models.OrderPayment{
		Vendor:        "vnpay",
		OrderID:       order.OrderID,
		TransactionID: response.TransactionID,
		Amount:        paymentAmount,
		Status:        "PENDING",
		Description:   "Đang chờ thanh toán VNPay",
		PaymentData:   []byte(fmt.Sprintf(`{"pay_url":"%s","qr_code":"%s"}`, response.PayURL, response.QRCode)),
	}

	if err := db.Create(&orderPayment).Error; err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// Return payment URL response
	paymentURL := map[string]any{
		"pay_url":        response.PayURL,
		"qr_code":        response.QRCode,
		"transaction_id": response.TransactionID,
		"deeplink":       response.Deeplink,
	}

	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"data":    paymentURL,
	})
}

// VNPayCallback handles VNPay payment callbacks
func VNPayCallback(c *gin.Context) {
	db := middlewares.GetDB(c)

	// Get callback data from query parameters (VNPay sends data via GET)
	callbackData := make(map[string]interface{})
	for key, values := range c.Request.URL.Query() {
		if len(values) > 0 {
			callbackData[key] = values[0]
		}
	}

	// Create VNPay payment provider
	provider := payment.NewProvider("vnpay")
	if provider == nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   "vnpay_provider_not_available",
		})
		return
	}

	// Process callback
	callbackResp, err := provider.ProcessCallback(callbackData)
	if err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"success": false,
			"error":   fmt.Sprintf("vnpay_callback_processing_failed: %v", err),
		})
		return
	}

	// Update order payment status
	var orderPayment models.OrderPayment
	if err := db.Where("transaction_id = ?", callbackResp.TransactionID).First(&orderPayment).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"success": false,
			"error":   "order_payment_not_found",
		})
		return
	}

	// Update payment status
	orderPayment.Status = callbackResp.Status
	orderPayment.Description = callbackResp.Message
	if err := db.Save(&orderPayment).Error; err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"success": false,
			"error":   err.Error(),
		})
		return
	}

	// If payment is successful, update order status
	if callbackResp.Success && callbackResp.Status == "COMPLETED" {
		var order models.Order
		if err := db.Where("order_id = ?", orderPayment.OrderID).First(&order).Error; err == nil {
			// Update order status to paid
			order.Status = "PAID"
			db.Save(&order)
		}
	}

	// Return success response for VNPay
	c.JSON(http.StatusOK, gin.H{
		"success": true,
		"message": "VNPay callback processed successfully",
		"data": gin.H{
			"transaction_id": callbackResp.TransactionID,
			"status":         callbackResp.Status,
			"amount":         callbackResp.Amount,
		},
	})
}
